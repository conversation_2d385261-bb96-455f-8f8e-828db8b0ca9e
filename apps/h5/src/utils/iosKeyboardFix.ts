/**
 * iOS键盘弹出修复工具函数
 * 专门处理iOS设备上键盘弹出时的各种问题
 */

/**
 * 检测是否为iOS设备
 */
export const isIOSDevice = (): boolean => {
  if (typeof window === 'undefined') return false
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检测是否支持visualViewport API
 */
export const supportsVisualViewport = (): boolean => {
  if (typeof window === 'undefined') return false
  return 'visualViewport' in window && window.visualViewport !== null
}

/**
 * 修复iOS键盘弹出时的滚动问题
 * 当输入框获得焦点时，确保它在可视区域内
 */
export const fixIOSKeyboardScroll = (inputElement: HTMLElement) => {
  if (!isIOSDevice()) return

  const handleFocus = () => {
    // 延迟执行，等待键盘完全弹出
    setTimeout(() => {
      // 获取输入框相对于视口的位置
      const rect = inputElement.getBoundingClientRect()
      const viewportHeight = window.visualViewport?.height || window.innerHeight

      // 如果输入框被键盘遮挡
      if (rect.bottom > viewportHeight) {
        // 计算需要滚动的距离
        const scrollOffset = rect.bottom - viewportHeight + 20 // 额外20px边距

        // 平滑滚动到合适位置
        window.scrollBy({
          top: scrollOffset,
          behavior: 'smooth',
        })
      }
    }, 300) // iOS键盘动画通常需要300ms
  }

  const handleBlur = () => {
    // 键盘收起时的处理（如果需要）
  }

  inputElement.addEventListener('focus', handleFocus)
  inputElement.addEventListener('blur', handleBlur)

  // 返回清理函数
  return () => {
    inputElement.removeEventListener('focus', handleFocus)
    inputElement.removeEventListener('blur', handleBlur)
  }
}

/**
 * 为弹窗添加键盘适配样式
 */
export const addKeyboardAdaptiveStyles = () => {
  if (!isIOSDevice() || !supportsVisualViewport()) return

  const style = document.createElement('style')
  style.textContent = `
    /* iOS键盘适配样式 */
    @supports (height: 100dvh) {
      .keyboard-adaptive-popup {
        max-height: 100dvh !important;
      }
    }
    
    /* 为不支持dvh的设备提供fallback */
    .keyboard-adaptive-popup {
      max-height: calc(var(--vh, 1vh) * 100) !important;
      transition: max-height 0.3s ease-out;
    }
    
    /* 键盘弹出时的特殊处理 */
    .keyboard-visible .keyboard-adaptive-popup {
      transform: translateY(0) !important;
    }
  `

  document.head.appendChild(style)

  return () => {
    document.head.removeChild(style)
  }
}

/**
 * 监听键盘状态变化
 */
export const watchKeyboardState = (callback: (isVisible: boolean, height: number) => void) => {
  if (!isIOSDevice() || !supportsVisualViewport()) return

  const initialHeight = window.visualViewport!.height

  const handleViewportChange = () => {
    const currentHeight = window.visualViewport!.height
    const heightDiff = initialHeight - currentHeight
    const isKeyboardVisible = heightDiff > 150 // 键盘高度阈值

    callback(isKeyboardVisible, heightDiff)

    // 更新body类名，方便CSS选择器使用
    if (isKeyboardVisible) {
      document.body.classList.add('keyboard-visible')
    } else {
      document.body.classList.remove('keyboard-visible')
    }
  }

  window.visualViewport!.addEventListener('resize', handleViewportChange)

  return () => {
    window.visualViewport!.removeEventListener('resize', handleViewportChange)
    document.body.classList.remove('keyboard-visible')
  }
}
