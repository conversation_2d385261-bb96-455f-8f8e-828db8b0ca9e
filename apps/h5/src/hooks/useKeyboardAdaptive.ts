'use client'
import { useEffect, useState } from 'react'

/**
 * iOS键盘适配Hook
 * 专门处理弹窗在iOS设备上键盘弹出时的适配问题
 */
export const useKeyboardAdaptive = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)
  const [adaptiveHeight, setAdaptiveHeight] = useState<string>('65rem')

  useEffect(() => {
    // 检测是否为iOS设备
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    if (!isIOS) return

    // 检测是否支持visualViewport API
    if (!window.visualViewport) return

    const initialHeight = window.visualViewport.height

    const handleViewportChange = () => {
      const currentHeight = window.visualViewport!.height
      const heightDiff = initialHeight - currentHeight

      // 键盘弹出的阈值（通常键盘高度 > 150px）
      const keyboardThreshold = 150
      const isKeyboardOpen = heightDiff > keyboardThreshold

      setIsKeyboardVisible(isKeyboardOpen)
      setKeyboardHeight(isKeyboardOpen ? heightDiff : 0)

      if (isKeyboardOpen) {
        // 键盘弹出时，调整弹窗高度
        // 预留一些空间给弹窗内容和按钮
        const availableHeight = currentHeight - 60 // 60px为顶部安全距离
        setAdaptiveHeight(`${availableHeight}px`)
      } else {
        // 键盘收起时，恢复默认高度
        setAdaptiveHeight('65rem')
      }
    }

    // 监听viewport变化
    window.visualViewport.addEventListener('resize', handleViewportChange)

    // 初始化
    handleViewportChange()

    return () => {
      window.visualViewport?.removeEventListener('resize', handleViewportChange)
    }
  }, [])

  return {
    keyboardHeight,
    isKeyboardVisible,
    adaptiveHeight,
  }
}
